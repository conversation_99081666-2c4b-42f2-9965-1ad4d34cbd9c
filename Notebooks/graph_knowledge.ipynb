from zep_cloud.external_clients.ontology import EntityModel, EntityText, EdgeModel, EntityBoolean,EntityInt
from pydantic import Field
from typing import Optional
from datetime import datetime

class TradingUser(EntityModel):
    """
    Represents a trading platform user profile.
    """
    username: EntityText = Field(description="The username of the trader", default=None)
    display_name: EntityText = Field(description="The display name of the trader", default=None)
    location: EntityText = Field(description="Geographic location of the trader", default=None)
    profile_type: EntityText = Field(description="Type of profile: regular, premium, verified, etc.", default=None)
    reputation_score: EntityInt = Field(description="Numerical reputation score on the platform", default=None)
    friends_count: EntityInt = Field(description="Number of friends/connections the user has", default=None)

class TradingProduct(EntityModel):
    """
    Represents a trading product (EA, indicator, signal, etc.).
    """
    product_title: EntityText = Field(description="The title/name of the trading product", default=None)
    category: EntityText = Field(description="Product category: Experts, Indicators, Scripts, etc.", default=None)
    product_type: EntityText = Field(description="Type of product: EA, indicator, signal, etc.", default=None)

class Achievement(EntityModel):
    """
    Represents a platform achievement or badge.
    """
    achievement_type: EntityText = Field(description="Type of achievement: forum, market, profile, etc.", default=None)
    achievement_name: EntityText = Field(description="Name of the achievement", default=None)
    achievement_level: EntityInt = Field(description="Level or count for the achievement", default=None)

class ProductReview(EdgeModel):
    """
    Represents a user reviewing a trading product.
    """
    product_title: EntityText = Field(description="The title of the product being reviewed", default=None)
    review_text: EntityText = Field(description="The text content of the review", default=None)
    rating_quality: EntityInt = Field(description="Quality rating given (1-5)", default=None)
    rating_reliability: EntityInt = Field(description="Reliability rating given (1-5)", default=None)
    rating_support: EntityInt = Field(description="Support rating given (1-5)", default=None)
    review_sentiment: EntityText = Field(description="Overall sentiment: positive, negative, neutral", default=None)

class ProductComment(EdgeModel):
    """
    Represents a user commenting on a trading product.
    """
    product_title: EntityText = Field(description="The title of the product being commented on", default=None)
    comment_text: EntityText = Field(description="The text content of the comment", default=None)
    comment_type: EntityText = Field(description="Type of comment: question, feedback, issue, etc.", default=None)
    is_question: EntityBoolean = Field(description="Whether the comment is asking a question", default=None)

class HasAchievement(EdgeModel):
    """
    Represents a user having earned an achievement.
    """
    achievement_name: EntityText = Field(description="Name of the achievement earned", default=None)
    achievement_level: EntityInt = Field(description="Level or count of the achievement", default=None)
    achievement_category: EntityText = Field(description="Category: forum, market, profile, etc.", default=None)
    is_active: EntityBoolean = Field(description="Whether the achievement is currently active/earned", default=None)

class TradingInterest(EdgeModel):
    """
    Represents a user's interest in specific trading topics/products.
    """
    interest_type: EntityText = Field(description="Type of interest: EA, scalping, BTC, forex, etc.", default=None)
    engagement_level: EntityText = Field(description="Level of engagement: high, medium, low", default=None)
    product_category: EntityText = Field(description="Product category of interest", default=None)

class LocationBased(EdgeModel):
    """
    Represents location-based information about a user.
    """
    country: EntityText = Field(description="Country where the user is located", default=None)
    region: EntityText = Field(description="Geographic region: Europe, Asia, Americas, etc.", default=None)
    timezone_preference: EntityText = Field(description="Likely timezone preference based on location", default=None)

def create_entities_from_user_data(user_data):
    """
    Create entity instances from MQL5 user data.
    """
    entities = []

    # Create TradingUser entity
    user_entity = TradingUser(
        username=user_data.get('username'),
        display_name=user_data.get('display_name'),
        location=user_data.get('location'),
        profile_type=user_data.get('profile_type'),
        reputation_score=user_data.get('reputation_score'),
        friends_count=user_data.get('friends_count')
    )
    entities.append(user_entity)

    # Create TradingProduct entities from reviews and comments
    activity = user_data.get('activity', {})

    # From reviews
    for review in activity.get('reviews', []):
        product_entity = TradingProduct(
            product_title=review.get('title', '').replace('Review for product ', ''),
            category=review.get('category'),
            product_type='EA' if review.get('category') == 'Experts' else 'Other'
        )
        entities.append(product_entity)

    # From comments
    for comment in activity.get('comments', []):
        product_entity = TradingProduct(
            product_title=comment.get('title', '').replace('Comment for product ', ''),
            category=comment.get('category'),
            product_type='EA' if comment.get('category') == 'Experts' else 'Other'
        )
        entities.append(product_entity)

    # Create Achievement entities
    achievements = user_data.get('achievements', {})
    for achievement_name, level in achievements.items():
        if level is not None and level > 0:
            achievement_entity = Achievement(
                achievement_type=achievement_name.split('_')[0],  # e.g., 'forum', 'market'
                achievement_name=achievement_name,
                achievement_level=level
            )
            entities.append(achievement_entity)

    return entities

def create_edges_from_user_data(user_data):
    """
    Create edge instances from MQL5 user data.
    """
    edges = []
    user_id = user_data.get('user_id')
    activity = user_data.get('activity', {})

    # Create ProductReview edges
    for review in activity.get('reviews', []):
        # Parse rating string like "Quality:1,Reliability:1,Support:1"
        rating_parts = review.get('rating', '').split(',')
        quality_rating = None
        reliability_rating = None
        support_rating = None

        for part in rating_parts:
            if ':' in part:
                key, value = part.split(':')
                if 'Quality' in key:
                    quality_rating = int(value)
                elif 'Reliability' in key:
                    reliability_rating = int(value)
                elif 'Support' in key:
                    support_rating = int(value)

        # Determine sentiment based on ratings
        avg_rating = sum(filter(None, [quality_rating, reliability_rating, support_rating])) / 3
        sentiment = 'positive' if avg_rating >= 4 else 'negative' if avg_rating <= 2 else 'neutral'

        review_edge = ProductReview(
            product_title=review.get('title', '').replace('Review for product ', ''),
            review_text=review.get('text'),
            rating_quality=quality_rating,
            rating_reliability=reliability_rating,
            rating_support=support_rating,
            review_sentiment=sentiment
        )
        edges.append(review_edge)

    # Create ProductComment edges
    for comment in activity.get('comments', []):
        is_question = '?' in comment.get('text', '')
        comment_type = 'question' if is_question else 'feedback'

        comment_edge = ProductComment(
            product_title=comment.get('title', '').replace('Comment for product ', ''),
            comment_text=comment.get('text'),
            comment_type=comment_type,
            is_question=is_question
        )
        edges.append(comment_edge)

    # Create HasAchievement edges
    achievements = user_data.get('achievements', {})
    for achievement_name, level in achievements.items():
        if level is not None and level > 0:
            achievement_edge = HasAchievement(
                achievement_name=achievement_name,
                achievement_level=level,
                achievement_category=achievement_name.split('_')[0],
                is_active=True
            )
            edges.append(achievement_edge)

    # Create TradingInterest edges based on activity
    interests = set()
    for review in activity.get('reviews', []):
        title = review.get('title', '').lower()
        if 'btc' in title or 'bitcoin' in title:
            interests.add('BTC')
        if 'scalping' in title:
            interests.add('scalping')
        if 'ea' in title or 'expert' in title:
            interests.add('EA')

    for interest in interests:
        interest_edge = TradingInterest(
            interest_type=interest,
            engagement_level='high',  # Based on active reviewing
            product_category='Experts'
        )
        edges.append(interest_edge)

    # Create LocationBased edge
    location = user_data.get('location')
    if location:
        # Simple region mapping
        region_map = {
            'Croatia': 'Europe',
            'Germany': 'Europe',
            'USA': 'Americas',
            'Singapore': 'Asia'
        }

        location_edge = LocationBased(
            country=location,
            region=region_map.get(location, 'Unknown'),
            timezone_preference='CET' if location in ['Croatia', 'Germany'] else 'Unknown'
        )
        edges.append(location_edge)

    return edges

# Sample user data (your actual data)
sample_user_data = {
    'profile_url': 'https://www.mql5.com/en/users/haap_1',
    'scraped_at': '2025-06-23T19:55:58.965463',
    'market_id': 'mql5',
    'user_id': 'haap_1',
    'username': 'haap_1',
    'display_name': 'HaAP_1',
    'avatar_url': 'https://c.mql5.com/avatar/avatar_na2_big.png',
    'location': 'Croatia',
    'reputation_score': 109,
    'profile_type': 'regular',
    'friends_count': None,
    'achievements': {
        'profile_openness': 2,
        'forum_reader': 5,
        'market_feedback_poster': 3
    },
    'activity': {
        'reviews': [
            {
                'date': '1736984407',
                'title': 'Review for product The Infinity EA MT5',
                'text': "EA's problems with AI. They show you in the past (back tests) how to become a millionaire in no time. The reality looks different.",
                'rating': 'Quality:1,Reliability:1,Support:1',
                'category': 'Experts'
            },
            {
                'date': '1736921780',
                'title': 'Review for product Quantum Queen MT5',
                'text': "I'm thrilled with your EA Quantum Queen MT5. Even if trends are in the opposite direction, you have very good exit scenarios.",
                'rating': 'Quality:5,Reliability:5,Support:5',
                'category': 'Experts'
            }
        ],
        'comments': [
            {
                'title': 'Comment for product Hipercube lynx BTC Scalping',
                'text': 'Hello Adrian! I bought your EA Hipercube Lynx BTC Scalping. Can I also use this programme for other currency pairs?',
                'category': 'Experts'
            }
        ]
    }
}

# Create entities and edges from the sample data
entities = create_entities_from_user_data(sample_user_data)
edges = create_edges_from_user_data(sample_user_data)

print("=== ENTITIES CREATED ===")
for i, entity in enumerate(entities):
    print(f"{i+1}. {type(entity).__name__}: {entity}")

print("\n=== EDGES CREATED ===")
for i, edge in enumerate(edges):
    print(f"{i+1}. {type(edge).__name__}: {edge}")