from zep_cloud.external_clients.ontology import <PERSON><PERSON>tyModel, EntityText, EdgeModel, EntityBoolean,EntityInt
from pydantic import Field
from typing import Optional
from datetime import datetime
import requests
import json
import uuid
import os
from openai import OpenAI
import rich

from dotenv import load_dotenv
from zep_cloud.client import Zep
from zep_cloud import Message

load_dotenv()

zep = Zep(api_key=os.environ.get("ZEP_API_KEY"))

oai_client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
)


def fetch_job_results(job_id):
    """Fetch job results from Voyagr scraping service"""
    url = f'https://voyagrscrapy-bcj9g.kinsta.app/jobs/{job_id}/results'
    headers = {
        'Cookie': '__cf_bm=vxfJ6XEypw_1.qI4iN9K_pP8xPKbC8nooM2AiLIShMc-1750712633-1.0.1.1-lCEH28or90vsAixQbhZklxKCcuuZBOnolhCxdEYVT4p4eclfJNMLasBxKXGmjKdgSivPht4BhkR5nCxQXUwLKdGnVoHX3_hxGlu9zSzEe7c'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return None

# Usage
job_id = "61c52944-f23e-4ab2-9404-7eb64d561311"
data = fetch_job_results(job_id)['results']

if data:
    print("Successfully retrieved data:")
    print(json.dumps(data, indent=2))

# Selecting idx 50 
sample_user_data = data[50]

class TradingUser(EntityModel):
    """
    Represents a trading platform user profile.
    """
    username: EntityText = Field(description="The username of the trader", default=None)
    display_name: EntityText = Field(description="The display name of the trader", default=None)
    location: EntityText = Field(description="Geographic location of the trader", default=None)
    profile_type: EntityText = Field(description="Type of profile: regular, premium, verified, etc.", default=None)
    reputation_score: EntityInt = Field(description="Numerical reputation score on the platform", default=None)
    friends_count: EntityInt = Field(description="Number of friends/connections the user has", default=None)

class Achievement(EntityModel):
    """
    Represents a platform achievement or badge.
    """
    achievement_type: EntityText = Field(description="Type of achievement: forum, market, profile, etc.", default=None)
    achievement_name: EntityText = Field(description="Name of the achievement", default=None)
    achievement_level: EntityInt = Field(description="Level or count for the achievement", default=None)

class ProductInteraction(EdgeModel):
    """
    Represents user's interaction with trading products (reviews, comments).
    Consolidated model focusing on user behavior patterns.
    """
    product_name: EntityText = Field(description="Name of the product interacted with", default=None)
    interaction_type: EntityText = Field(description="Type: review, comment, question", default=None)
    sentiment: EntityText = Field(description="User sentiment: positive, negative, neutral", default=None)
    engagement_level: EntityText = Field(description="Engagement: high, medium, low (based on text length)", default=None)
    product_category: EntityText = Field(description="Category: Experts, Indicators, etc.", default=None)
    average_rating: EntityInt = Field(description="Average rating if review (1-5)", default=None)

class HasAchievement(EdgeModel):
    """
    Represents a user having earned an achievement.
    """
    achievement_name: EntityText = Field(description="Name of the achievement earned", default=None)
    achievement_level: EntityInt = Field(description="Level or count of the achievement", default=None)
    achievement_category: EntityText = Field(description="Category: forum, market, profile, etc.", default=None)
    is_active: EntityBoolean = Field(description="Whether the achievement is currently active/earned", default=None)

class TradingInterest(EdgeModel):
    """
    Represents user's inferred trading interests from their activity.
    """
    interest_type: EntityText = Field(description="Type of interest: EA, scalping, BTC, forex, etc.", default=None)
    confidence_level: EntityText = Field(description="Confidence in interest: high, medium, low", default=None)
    evidence_source: EntityText = Field(description="Source of evidence: reviews, comments, products", default=None)

class LocationBased(EdgeModel):
    """
    Represents location-based information about a user.
    """
    country: EntityText = Field(description="Country where the user is located", default=None)
    region: EntityText = Field(description="Geographic region: Europe, Asia, Americas, etc.", default=None)
    timezone_preference: EntityText = Field(description="Likely timezone preference based on location", default=None)

class UserBehaviorPattern(EdgeModel):
    """
    Represents overall user behavior patterns derived from their activity.
    """
    activity_level: EntityText = Field(description="Activity level: high, medium, low", default=None)
    engagement_type: EntityText = Field(description="Primary engagement: reviewer, commenter, questioner", default=None)
    sentiment_tendency: EntityText = Field(description="Overall sentiment tendency: positive, negative, mixed, neutral", default=None)
    communication_style: EntityText = Field(description="Communication style: detailed, concise, questioning", default=None)

def create_user_entities(user_data):
    """
    Create user-focused entities from MQL5 data.
    Only creates user and achievement entities.
    """
    entities = []

    # Main user entity
    user_entity = TradingUser(
        username=user_data.get('username'),
        display_name=user_data.get('display_name'),
        location=user_data.get('location'),
        profile_type=user_data.get('profile_type'),
        reputation_score=user_data.get('reputation_score'),
        friends_count=user_data.get('friends_count')
    )
    entities.append(user_entity)

    # Achievement entities (only active ones)
    achievements = user_data.get('achievements', {})
    for achievement_name, level in achievements.items():
        if level is not None and level > 0:
            achievement_entity = Achievement(
                achievement_type=achievement_name.split('_')[0],
                achievement_name=achievement_name,
                achievement_level=level
            )
            entities.append(achievement_entity)

    return entities

def create_user_behavior_edges(user_data):
    """
    Create user behavior-focused edges from MQL5 data.
    Focus on patterns and behaviors rather than individual product details.
    """
    edges = []
    activity = user_data.get('activity', {})
    reviews = activity.get('reviews', [])
    comments = activity.get('comments', [])

    # Create ProductInteraction edges for reviews
    for review in reviews:
        # Calculate average rating
        avg_rating = None
        if review.get('rating'):
            rating_parts = review.get('rating', '').split(',')
            ratings = []
            for part in rating_parts:
                if ':' in part:
                    try:
                        ratings.append(int(part.split(':')[1]))
                    except:
                        pass
            if ratings:
                avg_rating = sum(ratings) / len(ratings)

        # Determine sentiment and engagement
        sentiment = 'positive' if avg_rating and avg_rating >= 4 else 'negative' if avg_rating and avg_rating <= 2 else 'neutral'
        text_length = len(review.get('text', ''))
        engagement = 'high' if text_length > 200 else 'medium' if text_length > 50 else 'low'

        interaction = ProductInteraction(
            product_name=review.get('title', '').replace('Review for product ', ''),
            interaction_type='review',
            sentiment=sentiment,
            engagement_level=engagement,
            product_category=review.get('category'),
            average_rating=avg_rating
        )
        edges.append(interaction)

    # Create ProductInteraction edges for comments
    for comment in comments:
        is_question = '?' in comment.get('text', '')
        text_length = len(comment.get('text', ''))
        engagement = 'high' if text_length > 200 else 'medium' if text_length > 50 else 'low'

        interaction = ProductInteraction(
            product_name=comment.get('title', '').replace('Comment for product ', ''),
            interaction_type='question' if is_question else 'comment',
            sentiment='neutral',  # Comments don't have ratings
            engagement_level=engagement,
            product_category=comment.get('category'),
            average_rating=None
        )
        edges.append(interaction)

    return edges

def create_user_pattern_edges(user_data):
    """
    Create high-level user behavior pattern edges.
    Analyzes overall user behavior to create summary edges.
    """
    edges = []
    activity = user_data.get('activity', {})
    reviews = activity.get('reviews', [])
    comments = activity.get('comments', [])
    achievements = user_data.get('achievements', {})

    # Analyze overall activity patterns
    total_interactions = len(reviews) + len(comments)
    activity_level = 'high' if total_interactions > 10 else 'medium' if total_interactions > 3 else 'low'

    # Determine primary engagement type
    if len(reviews) > len(comments):
        engagement_type = 'reviewer'
    elif len(comments) > 0 and any('?' in c.get('text', '') for c in comments):
        engagement_type = 'questioner'
    elif len(comments) > 0:
        engagement_type = 'commenter'
    else:
        engagement_type = 'lurker'

    # Analyze sentiment tendency
    sentiments = []
    for review in reviews:
        if review.get('rating'):
            rating_parts = review.get('rating', '').split(',')
            ratings = []
            for part in rating_parts:
                if ':' in part:
                    try:
                        ratings.append(int(part.split(':')[1]))
                    except:
                        pass
            if ratings:
                avg_rating = sum(ratings) / len(ratings)
                sentiments.append('positive' if avg_rating >= 4 else 'negative' if avg_rating <= 2 else 'neutral')

    if sentiments:
        positive_count = sentiments.count('positive')
        negative_count = sentiments.count('negative')
        if positive_count > negative_count * 2:
            sentiment_tendency = 'positive'
        elif negative_count > positive_count * 2:
            sentiment_tendency = 'negative'
        else:
            sentiment_tendency = 'mixed'
    else:
        sentiment_tendency = 'neutral'

    # Analyze communication style
    all_texts = [r.get('text', '') for r in reviews] + [c.get('text', '') for c in comments]
    avg_text_length = sum(len(text) for text in all_texts) / len(all_texts) if all_texts else 0
    question_ratio = sum(1 for text in all_texts if '?' in text) / len(all_texts) if all_texts else 0

    if question_ratio > 0.5:
        communication_style = 'questioning'
    elif avg_text_length > 150:
        communication_style = 'detailed'
    else:
        communication_style = 'concise'

    # Create UserBehaviorPattern edge
    behavior_pattern = UserBehaviorPattern(
        activity_level=activity_level,
        engagement_type=engagement_type,
        sentiment_tendency=sentiment_tendency,
        communication_style=communication_style
    )
    edges.append(behavior_pattern)

    return edges

def create_additional_edges(user_data):
    """
    Create additional user-focused edges (achievements, interests, location).
    """
    edges = []

    # HasAchievement edges
    achievements = user_data.get('achievements', {})
    for achievement_name, level in achievements.items():
        if level is not None and level > 0:
            achievement_edge = HasAchievement(
                achievement_name=achievement_name,
                achievement_level=level,
                achievement_category=achievement_name.split('_')[0],
                is_active=True
            )
            edges.append(achievement_edge)

    # TradingInterest edges (inferred from activity)
    activity = user_data.get('activity', {})
    interests = set()

    # Analyze reviews and comments for interests
    all_texts = []
    for review in activity.get('reviews', []):
        all_texts.append(review.get('title', '').lower() + ' ' + review.get('text', '').lower())
    for comment in activity.get('comments', []):
        all_texts.append(comment.get('title', '').lower() + ' ' + comment.get('text', '').lower())

    combined_text = ' '.join(all_texts)

    # Interest detection
    interest_keywords = {
        'BTC': ['btc', 'bitcoin', 'crypto'],
        'scalping': ['scalping', 'scalp'],
        'EA': ['ea', 'expert advisor', 'expert'],
        'forex': ['forex', 'currency', 'eur', 'usd', 'gbp'],
        'AI': ['ai', 'artificial intelligence', 'machine learning']
    }

    for interest, keywords in interest_keywords.items():
        if any(keyword in combined_text for keyword in keywords):
            confidence = 'high' if sum(combined_text.count(kw) for kw in keywords) > 2 else 'medium'
            interest_edge = TradingInterest(
                interest_type=interest,
                confidence_level=confidence,
                evidence_source='reviews_comments'
            )
            edges.append(interest_edge)

    # LocationBased edge
    location = user_data.get('location')
    if location:
        region_map = {
            'Croatia': 'Europe', 'Germany': 'Europe', 'France': 'Europe',
            'USA': 'Americas', 'Canada': 'Americas', 'Brazil': 'Americas',
            'Singapore': 'Asia', 'Japan': 'Asia', 'China': 'Asia'
        }

        timezone_map = {
            'Croatia': 'CET', 'Germany': 'CET', 'France': 'CET',
            'USA': 'EST/PST', 'Canada': 'EST/PST',
            'Singapore': 'SGT', 'Japan': 'JST'
        }

        location_edge = LocationBased(
            country=location,
            region=region_map.get(location, 'Unknown'),
            timezone_preference=timezone_map.get(location, 'Unknown')
        )
        edges.append(location_edge)

    return edges

def create_complete_user_profile(user_data):
    """
    Create complete user profile with all entities and edges.
    Returns structured data ready for Zep ingestion.
    """
    # Create all entities and edges
    entities = create_user_entities(user_data)
    interaction_edges = create_user_behavior_edges(user_data)
    pattern_edges = create_user_pattern_edges(user_data)
    additional_edges = create_additional_edges(user_data)

    # Combine all edges
    all_edges = interaction_edges + pattern_edges + additional_edges

    return {
        'user_id': user_data.get('user_id'),
        'entities': entities,
        'edges': all_edges,
        'summary': {
            'total_entities': len(entities),
            'total_edges': len(all_edges),
            'interaction_count': len(interaction_edges),
            'behavior_patterns': len(pattern_edges),
            'additional_insights': len(additional_edges)
        }
    }

sample_user_data

new_user = zep.user.add(
    user_id = sample_user_data['user_id'],
    first_name=sample_user_data['display_name'],
    email="",
    metadata={
        "platform": "mql5",
        "location": sample_user_data.get("location", ""),
        "reputation_score": sample_user_data.get("reputation_score", 0),
        "friends_count": sample_user_data.get("friends_count", 0),
        "profile_type": sample_user_data.get("profile_type", "regular")
    }
)

for review in sample_user_data['activity']['reviews']:
    new_episode = zep.graph.add(
        data = json.dumps(review),
        type = "json",
        user_id = sample_user_data['user_id'],
        source_description = "Review for product " + review['title']
    )

sample_user_data['activity']['reviews']
class ReviewModel(BaseModel):
    date: str
    title: str
    text: str
    rating: str
    category: str

# Create complete user profile
user_profile = create_complete_user_profile(sample_user_data)

print("=== USER PROFILE SUMMARY ===")
print(f"User ID: {user_profile['user_id']}")
print(f"Total Entities: {user_profile['summary']['total_entities']}")
print(f"Total Edges: {user_profile['summary']['total_edges']}")
print(f"Product Interactions: {user_profile['summary']['interaction_count']}")
print(f"Behavior Patterns: {user_profile['summary']['behavior_patterns']}")
print(f"Additional Insights: {user_profile['summary']['additional_insights']}")

print("\n=== ENTITIES ===")
for i, entity in enumerate(user_profile['entities']):
    print(f"{i+1}. {type(entity).__name__}: {entity}")

print("\n=== EDGES (First 5) ===")
for i, edge in enumerate(user_profile['edges'][:5]):
    print(f"{i+1}. {type(edge).__name__}: {edge}")

if len(user_profile['edges']) > 5:
    print(f"... and {len(user_profile['edges']) - 5} more edges")