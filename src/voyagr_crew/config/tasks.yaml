login_to_mql5:
  description: Automate login to mql5.com using Selenium in non-headless mode with
    provided credentials.
  expected_output: Successful login to mql5.com.
  async_execution: false
  agent: login_expert
extract_messages:
  description: Navigate the mql5.com inbox and extract message data, including conversations,
    and structure them into JSON format.
  expected_output: Structured JSON output of messages and conversations.
  async_execution: false
  agent: message_extractor
retrieve_user_data:
  description: Use Zep Cloud Memory to get user-specific data for message personalization.
  expected_output: Detailed user data for personalization.
  async_execution: false
  agent: user_data_researcher
create_targeted_icebreakers:
  description: Analyze user data from Zep Cloud Memory to create highly targeted icebreakers
    for outreach messages.
  expected_output: Personalized outreach messages with targeted icebreakers.
  async_execution: false
  agent: icebreaker_creator
  context:
  - retrieve_user_data
