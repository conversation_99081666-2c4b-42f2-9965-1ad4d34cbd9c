import os
import json
from crewai.tools import BaseTool
from typing import Type, Optional, Dict, Any
from pydantic import BaseModel, Field
from zep_cloud.client import Zep


class ZepKnowledgeGraphInput(BaseModel):
    """Input schema for ZepKnowledgeGraphTool."""
    user_id: str = Field(..., description="The user ID to search for in the knowledge graph")
    search_query: Optional[str] = Field(None, description="Optional search query to filter specific information about the user")
    limit: Optional[int] = Field(10, description="Maximum number of results to return (default: 10)")
    scope: Optional[str] = Field("edges", description="Scope of search: 'edges' (default) or 'nodes'")


class ZepKnowledgeGraphTool(BaseTool):
    name: str = "Zep Knowledge Graph User Data Retriever"
    description: str = (
        "Retrieves user-specific information from Zep Cloud Knowledge Graph. "
        "This tool can fetch comprehensive user data, preferences, history, and contextual information "
        "stored in the knowledge graph for personalization purposes. Use this when you need to "
        "gather detailed user information for creating targeted, personalized content or responses."
    )
    args_schema: Type[BaseModel] = ZepKnowledgeGraphInput

    def __init__(self):
        super().__init__()
        api_key = os.environ.get('ZEP_API_KEY')
        if not api_key:
            raise ValueError("ZEP_API_KEY environment variable is required")
        self._client = Zep(api_key=api_key)

    @property
    def client(self):
        return self._client

    def _run(self, user_id: str, search_query: Optional[str] = None, limit: Optional[int] = 10, scope: Optional[str] = "edges") -> str:
        """
        Retrieve user information from Zep Cloud Knowledge Graph.
        
        Args:
            user_id: The user ID to search for
            search_query: Optional search query to filter results
            limit: Maximum number of results to return
            scope: Scope of search - 'edges' or 'nodes'
            
        Returns:
            Formatted string containing user information
        """
        try:
            # First, try to get user node information
            user_info = self._get_user_node(user_id)
            
            # Search the knowledge graph for user information
            if search_query:
                # Use search with specific query
                search_results = self.client.graph.search(
                    user_id=user_id,
                    query=search_query,
                    scope=scope,
                    limit=limit
                )
            else:
                # Get general user information with broader query
                search_results = self.client.graph.search(
                    user_id=user_id,
                    query="What do we know about this user? What are their preferences, interests, and background?",
                    scope=scope,
                    limit=limit
                )
            
            # Format the results
            formatted_info = f"User Information for {user_id}:\n"
            formatted_info += "=" * 50 + "\n\n"
            
            # Add user node information if available
            if user_info:
                formatted_info += "User Profile:\n"
                formatted_info += user_info + "\n\n"
            
            # Add knowledge graph search results
            if search_results and hasattr(search_results, 'edges') and search_results.edges:
                formatted_info += "Knowledge Graph Data:\n"
                
                for i, edge in enumerate(search_results.edges, 1):
                    formatted_info += f"\nData Point {i}:\n"
                    
                    # Extract source and target information
                    if hasattr(edge, 'source') and edge.source:
                        formatted_info += f"  Source: {edge.source}\n"
                    
                    if hasattr(edge, 'target') and edge.target:
                        formatted_info += f"  Target: {edge.target}\n"
                    
                    # Extract edge data/properties - this contains the actual information
                    if hasattr(edge, 'data') and edge.data:
                        formatted_info += f"  Information: {edge.data}\n"
                    
                    # Extract any additional properties
                    if hasattr(edge, 'properties') and edge.properties:
                        formatted_info += f"  Properties: {edge.properties}\n"
                    
                    # Extract fact or relationship information
                    if hasattr(edge, 'fact') and edge.fact:
                        formatted_info += f"  Fact: {edge.fact}\n"
                
                formatted_info += f"\nTotal knowledge graph entries: {len(search_results.edges)}\n"
            else:
                if not user_info:
                    return f"No information found for user_id: {user_id}. The user may not exist in the knowledge graph or no data has been added yet."
                else:
                    formatted_info += "No additional knowledge graph data found.\n"
            
            return formatted_info.strip()
            
        except Exception as e:
            error_msg = f"Error retrieving user data from Zep Cloud: {str(e)}"
            return error_msg

    def _get_user_node(self, user_id: str) -> Optional[str]:
        """
        Get user node information from Zep Cloud.
        
        Args:
            user_id: The user ID to get node information for
            
        Returns:
            Formatted string with user node information or None
        """
        try:
            user_node_response = self.client.user.get_node(user_id=user_id)
            if user_node_response and hasattr(user_node_response, 'node') and user_node_response.node:
                node = user_node_response.node
                info = []
                
                if hasattr(node, 'properties') and node.properties:
                    for key, value in node.properties.items():
                        info.append(f"  {key}: {value}")
                
                if hasattr(node, 'name') and node.name:
                    info.append(f"  Name: {node.name}")
                    
                if hasattr(node, 'data') and node.data:
                    info.append(f"  Data: {node.data}")
                    
                return "\n".join(info) if info else None
                
        except Exception:
            # User node might not exist yet, which is normal
            pass
        
        return None
    
    def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """
        Helper method to get structured user context data.
        
        Args:
            user_id: The user ID to get context for
            
        Returns:
            Dictionary containing structured user context
        """
        try:
            # Get user node info
            user_node_info = self._get_user_node(user_id)
            
            # Search knowledge graph
            search_results = self.client.graph.search(
                user_id=user_id,
                query="What information do we have about this user?",
                scope="edges",
                limit=50  # Get more comprehensive data
            )
            
            context = {
                "user_id": user_id,
                "user_node": user_node_info,
                "data_points": [],
                "total_entries": 0
            }
            
            if search_results and hasattr(search_results, 'edges') and search_results.edges:
                for edge in search_results.edges:
                    data_point = {}
                    
                    if hasattr(edge, 'source'):
                        data_point['source'] = edge.source
                    if hasattr(edge, 'target'):
                        data_point['target'] = edge.target
                    if hasattr(edge, 'data'):
                        data_point['data'] = edge.data
                    if hasattr(edge, 'properties'):
                        data_point['properties'] = edge.properties
                    if hasattr(edge, 'fact'):
                        data_point['fact'] = edge.fact
                    
                    context["data_points"].append(data_point)
                
                context["total_entries"] = len(search_results.edges)
            
            return context
            
        except Exception as e:
            return {
                "user_id": user_id,
                "error": str(e),
                "user_node": None,
                "data_points": [],
                "total_entries": 0
            }
    
    def create_comprehensive_user_graph(self, user_data: Dict[str, Any]) -> str:
        """
        Create a comprehensive user knowledge graph using Zep's advanced features.
        
        Args:
            user_data: Dictionary containing user information (from scraped data)
            
        Returns:
            Status message indicating success or failure
        """
        try:
            # Extract user information from the scraped data structure
            if "results" in user_data and len(user_data["results"]) > 0:
                user_info = user_data["results"][0]
            else:
                user_info = user_data
            
            user_id = user_info.get("user_id")
            if not user_id:
                return "Error: user_id not found in user data"
            
            result_msg = f"🚀 Creating comprehensive knowledge graph for {user_id}...\n"
            
            # Step 1: Create user in Zep Cloud with comprehensive metadata
            try:
                user_creation_data = {
                    "user_id": user_id,
                    "email": f"{user_id}@mql5.com",  # Default email since we don't have real email
                    "first_name": user_info.get("display_name", user_id),
                    "last_name": "",
                    "metadata": {
                        "platform": "mql5",
                        "profile_url": user_info.get("profile_url", ""),
                        "location": user_info.get("location", ""),
                        "reputation_score": user_info.get("reputation_score", 0),
                        "friends_count": user_info.get("friends_count", 0),
                        "profile_type": user_info.get("profile_type", "regular"),
                        "scraped_at": user_info.get("scraped_at", ""),
                        "avatar_url": user_info.get("avatar_url", "")
                    }
                }
                
                self.client.user.add(**user_creation_data)
                result_msg += "✅ User created successfully\n"
                
            except Exception as user_create_error:
                if "already exists" in str(user_create_error).lower():
                    result_msg += "ℹ️ User already exists, proceeding with graph creation\n"
                else:
                    return f"Error creating user: {str(user_create_error)}"
            
            # Step 2: Create comprehensive knowledge graph using structured entities
            entities_created = 0
            
            # 2.1: Add user profile as structured JSON entity
            profile_entity = {
                "entity_type": "user_profile",
                "platform": "mql5",
                "user_id": user_id,
                "display_name": user_info.get("display_name"),
                "location": user_info.get("location"),
                "reputation_score": user_info.get("reputation_score", 0),
                "friends_count": user_info.get("friends_count", 0),
                "profile_type": user_info.get("profile_type", "regular"),
                "profile_url": user_info.get("profile_url"),
                "avatar_url": user_info.get("avatar_url"),
                "timestamp": user_info.get("scraped_at", "")
            }
            
            try:
                self.client.graph.add(
                    user_id=user_id,
                    data=profile_entity,
                    type="json"
                )
                entities_created += 1
                result_msg += "✅ Added user profile entity\n"
            except Exception as e:
                result_msg += f"⚠️ Failed to add profile entity: {e}\n"
            
            # 2.2: Add trading reviews as structured entities
            activity = user_info.get("activity", {})
            reviews = activity.get("reviews", [])
            
            for i, review in enumerate(reviews[:3]):  # Limit to first 3 reviews
                review_entity = {
                    "entity_type": "trading_review",
                    "user_id": user_id,
                    "review_id": f"{user_id}_review_{i}",
                    "product_title": review.get("title", ""),
                    "review_text": review.get("text", "")[:500],  # Limit text length
                    "rating": review.get("rating", ""),
                    "category": review.get("category", ""),
                    "product_id": review.get("product_id", ""),
                    "date": review.get("date", ""),
                    "url": review.get("url", ""),
                    "module": review.get("module", "")
                }
                
                try:
                    self.client.graph.add(
                        user_id=user_id,
                        data=review_entity,
                        type="json"
                    )
                    entities_created += 1
                except Exception as e:
                    result_msg += f"⚠️ Failed to add review {i+1}: {e}\n"
            
            if reviews:
                result_msg += f"✅ Added {min(3, len(reviews))} review entities\n"
            
            # 2.3: Add achievements as structured data
            achievements = user_info.get("achievements", {})
            # Filter out null achievements and create meaningful achievement data
            active_achievements = {k: v for k, v in achievements.items() if v is not None and v > 0}
            
            if active_achievements:
                achievement_entity = {
                    "entity_type": "user_achievements",
                    "user_id": user_id,
                    "platform": "mql5",
                    "achievements": active_achievements,
                    "total_active_achievements": len(active_achievements),
                    "achievement_categories": list(active_achievements.keys())
                }
                
                try:
                    self.client.graph.add(
                        user_id=user_id,
                        data=achievement_entity,
                        type="json"
                    )
                    entities_created += 1
                    result_msg += "✅ Added achievements entity\n"
                except Exception as e:
                    result_msg += f"⚠️ Failed to add achievements: {e}\n"
            
            # 2.4: Add trading network entities
            friends_list = user_info.get("friends_list", [])
            if friends_list:
                # Add network summary entity
                unique_countries = set()
                for friend in friends_list[:10]:
                    name = friend.get("name", "")
                    if name:
                        # Simple heuristic to extract potential country/location info
                        parts = name.split()
                        if len(parts) > 1:
                            unique_countries.add(parts[-1])
                
                network_entity = {
                    "entity_type": "trading_network",
                    "user_id": user_id,
                    "total_friends": len(friends_list),
                    "sample_friend_count": min(10, len(friends_list)),
                    "network_diversity": "international" if len(unique_countries) > 3 else "regional",
                    "estimated_countries": list(unique_countries)[:5]
                }
                
                try:
                    self.client.graph.add(
                        user_id=user_id,
                        data=network_entity,
                        type="json"
                    )
                    entities_created += 1
                    result_msg += "✅ Added trading network entity\n"
                except Exception as e:
                    result_msg += f"⚠️ Failed to add network entity: {e}\n"
                
                # Add individual friend connections (limit to top 5 for relationship mapping)
                friend_connections = 0
                for i, friend in enumerate(friends_list[:5]):
                    friend_entity = {
                        "entity_type": "friend_connection",
                        "user_id": user_id,
                        "connection_id": f"{user_id}_friend_{i}",
                        "friend_name": friend.get("name", ""),
                        "friend_username": friend.get("username", ""),
                        "friend_profile_url": friend.get("profile_url", ""),
                        "connection_type": "mql5_friend",
                        "is_online": friend.get("is_online", False),
                        "has_avatar": bool(friend.get("avatar_url"))
                    }
                    
                    try:
                        self.client.graph.add(
                            user_id=user_id,
                            data=friend_entity,
                            type="json"
                        )
                        friend_connections += 1
                    except Exception as e:
                        result_msg += f"⚠️ Failed to add friend {i+1}: {e}\n"
                
                entities_created += friend_connections
                result_msg += f"✅ Added {friend_connections} friend connection entities\n"
            
            # Step 3: Create a comprehensive session with user context
            session_id = f"{user_id}_profile_session"
            try:
                # Add a session that represents the user's profile data
                self.client.memory.add_session(
                    user_id=user_id,
                    session_id=session_id,
                    metadata={
                        "session_type": "profile_import",
                        "platform": "mql5",
                        "import_date": user_info.get("scraped_at", ""),
                        "entities_created": entities_created
                    }
                )
                
                # Create a comprehensive summary message
                review_insights = ""
                if reviews:
                    first_review = reviews[0]
                    review_insights = f"""
                Recent Review Insights:
                - Reviewed: {first_review.get('title', 'Unknown product')}
                - Category: {first_review.get('category', 'Unknown')}
                - Rating: {first_review.get('rating', 'Not specified')}
                - Shows preference for quality trading tools
                """
                
                summary_message = f"""Comprehensive User Profile for {user_id}:
                
                BASIC INFO:
                - Display Name: {user_info.get('display_name', 'Unknown')}
                - Location: {user_info.get('location', 'Unknown')}
                - Reputation Score: {user_info.get('reputation_score', 0)} points
                - Profile Type: {user_info.get('profile_type', 'regular')}
                
                NETWORK & ENGAGEMENT:
                - Friends/Connections: {user_info.get('friends_count', 0)}
                - Active Achievements: {len(active_achievements)}
                - Reviews Posted: {len(reviews)}
                - Platform Engagement: Active reviewer and community member
                
                {review_insights}
                
                OUTREACH RECOMMENDATIONS:
                - User is engaged with trading community
                - Values quality and performance in trading tools
                - Has international network connections
                - Active in reviewing and feedback processes
                - Location-based approach may be effective (Singapore-based)
                """
                
                self.client.memory.add(
                    user_id=user_id,
                    session_id=session_id,
                    messages=[
                        {
                            "role": "system",
                            "content": summary_message
                        }
                    ]
                )
                
                result_msg += "✅ Created comprehensive user session with profile context\n"
            except Exception as e:
                result_msg += f"⚠️ Failed to create session: {e}\n"
            
            result_msg += f"\n🎯 Knowledge Graph Summary:\n"
            result_msg += f"- Total entities created: {entities_created}\n"
            result_msg += f"- User profile: ✅\n"
            result_msg += f"- Trading reviews: {len(reviews)} processed\n"
            result_msg += f"- Network connections: {min(5, len(friends_list))} mapped\n"
            result_msg += f"- Achievements: {len(active_achievements)} active\n"
            result_msg += f"- Session created: ✅\n"
            result_msg += f"\n🎉 {user_id} is ready for highly personalized outreach!"
            
            return result_msg
            
        except Exception as e:
            return f"Error in create_comprehensive_user_graph: {str(e)}"
    
    def create_user_from_json_file(self, json_file_path: str) -> str:
        """
        Create comprehensive user graph from a JSON file.
        
        Args:
            json_file_path: Path to the JSON file containing user data
            
        Returns:
            Status message
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            return self.create_comprehensive_user_graph(user_data)
            
        except Exception as e:
            return f"Error reading JSON file: {str(e)}"