import os
from crewai import Agent, Crew, Process, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
from crewai_tools import SeleniumScrapingTool
from crewai_tools import ScrapeWebsiteTool
from .tools import ZepKnowledgeGraphTool
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@CrewBase
class VoyagrCrew():
    """VoyagrCrew"""

    def __init__(self):
        super().__init__()
        self._setup_llms()

    def _setup_llms(self):
        """Initialize LLM configurations for different agent types"""
        # Primary LLM for complex reasoning tasks
        self.primary_llm = ChatOpenAI(
            model=os.getenv("DEFAULT_LLM_MODEL", "gpt-4"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.7")),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "2000")),
            api_key=os.getenv("OPENAI_API_KEY")
        )

        # Efficient LLM for simpler tasks
        self.efficient_llm = ChatOpenAI(
            model=os.getenv("FALLBACK_LLM_MODEL", "gpt-3.5-turbo"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.7")),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "1500")),
            api_key=os.getenv("OPENAI_API_KEY")
        )

    @agent
    def login_expert(self) -> Agent:
        return Agent(
            config=self.agents_config['login_expert'],
            tools=[SeleniumScrapingTool()],
            llm=self.efficient_llm,  # Use efficient LLM for automation tasks
            verbose=True
        )

    @agent
    def message_extractor(self) -> Agent:
        return Agent(
            config=self.agents_config['message_extractor'],
            tools=[ScrapeWebsiteTool()],
            llm=self.efficient_llm,  # Use efficient LLM for extraction tasks
            verbose=True
        )

    @agent
    def user_data_researcher(self) -> Agent:
        return Agent(
            config=self.agents_config['user_data_researcher'],
            tools=[ZepKnowledgeGraphTool()],
            llm=self.primary_llm,  # Use primary LLM for research and analysis
            verbose=True
        )

    @agent
    def icebreaker_creator(self) -> Agent:
        return Agent(
            config=self.agents_config['icebreaker_creator'],
            tools=[],
            llm=self.primary_llm,  # Use primary LLM for creative content generation
            verbose=True
        )


    @task
    def login_to_mql5(self) -> Task:
        return Task(
            config=self.tasks_config['login_to_mql5'],
            tools=[SeleniumScrapingTool()],
        )

    @task
    def extract_messages(self) -> Task:
        return Task(
            config=self.tasks_config['extract_messages'],
            tools=[ScrapeWebsiteTool()],
        )

    @task
    def retrieve_user_data(self) -> Task:
        return Task(
            config=self.tasks_config['retrieve_user_data'],
            tools=[ZepKnowledgeGraphTool()],
        )

    @task
    def create_targeted_icebreakers(self) -> Task:
        return Task(
            config=self.tasks_config['create_targeted_icebreakers'],
            tools=[],
        )


    @crew
    def crew(self) -> Crew:
        """Creates the VoyagrCrew"""
        return Crew(
            agents=self.agents, # Automatically created by the @agent decorator
            tasks=self.tasks, # Automatically created by the @task decorator
            process=Process.sequential,
            verbose=True,
        )
