import requests
import json

def fetch_job_results(job_id):
    """Fetch job results from Voyagr scraping service"""
    url = f'https://voyagrscrapy-bcj9g.kinsta.app/jobs/{job_id}/results'
    headers = {
        'Cookie': '__cf_bm=vxfJ6XEypw_1.qI4iN9K_pP8xPKbC8nooM2AiLIShMc-1750712633-1.0.1.1-lCEH28or90vsAixQbhZklxKCcuuZBOnolhCxdEYVT4p4eclfJNMLasBxKXGmjKdgSivPht4BhkR5nCxQXUwLKdGnVoHX3_hxGlu9zSzEe7c'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return None

# Usage
job_id = "61c52944-f23e-4ab2-9404-7eb64d561311"
data = fetch_job_results(job_id)

if data:
    print("Successfully retrieved data:")
    print(json.dumps(data, indent=2))