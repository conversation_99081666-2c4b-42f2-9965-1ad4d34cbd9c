[project]
name = "voyagr_crew"
version = "0.1.0"
description = "VoyagrCrew using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.119.0,<1.0.0",
    "ipykernel>=6.29.5",
    "langchain-openai>=0.1.0",
    "python-dotenv>=1.0.0",
    "selenium>=4.33.0",
    "webdriver-manager>=4.0.2",
    "zep-cloud>=2.0.0",
]

[project.scripts]
voyagr_crew = "voyagr_crew.main:run"
run_crew = "voyagr_crew.main:run"
train = "voyagr_crew.main:train"
replay = "voyagr_crew.main:replay"
test = "voyagr_crew.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
